"use client";

import { cn } from "@/lib/utils";
import React, { useState, useMemo } from "react";

/**
 * InteractiveGridPattern is a component that renders a grid pattern with interactive squares.
 * Features dynamic color algorithm with gradient transitions for enhanced visual appeal.
 *
 * @param width - The width of each square.
 * @param height - The height of each square.
 * @param squares - The number of squares in the grid. The first element is the number of horizontal squares, and the second element is the number of vertical squares.
 * @param className - The class name of the grid.
 * @param squaresClassName - The class name of the squares.
 */
interface InteractiveGridPatternProps extends React.SVGProps<SVGSVGElement> {
  width?: number;
  height?: number;
  squares?: [number, number]; // [horizontal, vertical]
  className?: string;
  squaresClassName?: string;
}

/**
 * HSL to RGB conversion utility
 */
const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
};

/**
 * Generate base grey color for grid squares
 * More visible grey background
 */
const generateBaseColor = (isDark: boolean = false): string => {
  if (isDark) {
    return "rgba(156, 163, 175, 0.2)"; // Light grey for dark mode - more visible
  } else {
    return "rgba(100, 116, 139, 0.2)"; // Dark grey for light mode - more visible
  }
};

/**
 * Generate random rainbow hover color for each grid square
 * Uses index as seed for consistent but random-looking colors
 */
const generateHoverColor = (index: number, isDark: boolean = false): string => {
  // Use index as seed for pseudo-random but consistent colors
  const seed = index * 2654435761; // Large prime number for better distribution
  const hue = (seed % 360); // Random hue from 0-360

  // Fixed saturation and lightness for vibrant colors
  const saturation = isDark ? 0.6 : 0.8; // Vibrant colors
  const lightness = isDark ? 0.6 : 0.5;  // Balanced brightness

  // Convert HSL to RGB
  const [r, g, b] = hslToRgb(hue, saturation, lightness);

  // Normal opacity for hover
  const opacity = isDark ? 0.3 : 0.25;

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * The InteractiveGridPattern component with dynamic color algorithm.
 *
 * @see InteractiveGridPatternProps for the props interface.
 * @returns A React component with enhanced visual effects.
 */
export function InteractiveGridPattern({
  width = 40,
  height = 40,
  squares = [24, 24],
  className,
  squaresClassName,
  ...props
}: InteractiveGridPatternProps) {
  const [horizontal, vertical] = squares;
  const [hoveredSquare, setHoveredSquare] = useState<number | null>(null);

  // Detect dark mode using CSS media query
  const [isDark, setIsDark] = useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const checkDarkMode = () => {
      // Check both system preference and document class
      const systemDark = mediaQuery.matches;
      const documentDark = document.documentElement.classList.contains('dark');
      setIsDark(systemDark || documentDark);
    };

    checkDarkMode();
    mediaQuery.addEventListener('change', checkDarkMode);

    // Also listen for class changes on document
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => {
      mediaQuery.removeEventListener('change', checkDarkMode);
      observer.disconnect();
    };
  }, []);

  // Memoize color calculations for performance
  const squareColors = useMemo(() => {
    return Array.from({ length: horizontal * vertical }).map((_, index) => {
      return {
        baseColor: generateBaseColor(isDark),
        hoverColor: generateHoverColor(index, isDark),
      };
    });
  }, [horizontal, vertical, isDark]);

  return (
    <svg
      width={width * horizontal}
      height={height * vertical}
      className={cn(
        "absolute inset-0 h-full w-full",
        className,
      )}
      {...props}
    >
      {/* Simple filter for subtle glow effect */}
      <defs>
        <filter id="subtle-glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
        </filter>
      </defs>

      {/* Render grid squares with simple colors */}
      {Array.from({ length: horizontal * vertical }).map((_, index) => {
        const x = (index % horizontal) * width;
        const y = Math.floor(index / horizontal) * height;
        const isHovered = hoveredSquare === index;

        return (
          <rect
            key={index}
            x={x}
            y={y}
            width={width}
            height={height}
            fill={isHovered ? squareColors[index].hoverColor : squareColors[index].baseColor}
            stroke={isDark ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.15)"}
            strokeWidth="0.5"
            className={cn(
              "transition-all duration-300 ease-out cursor-pointer",
              squaresClassName,
            )}
            onMouseEnter={() => setHoveredSquare(index)}
            onMouseLeave={() => setHoveredSquare(null)}
          />
        );
      })}
    </svg>
  );
}
