"use client";

import { cn } from "@/lib/utils";
import React, { useState, useMemo } from "react";

/**
 * InteractiveGridPattern is a component that renders a grid pattern with interactive squares.
 * Features dynamic color algorithm with gradient transitions for enhanced visual appeal.
 *
 * @param width - The width of each square.
 * @param height - The height of each square.
 * @param squares - The number of squares in the grid. The first element is the number of horizontal squares, and the second element is the number of vertical squares.
 * @param className - The class name of the grid.
 * @param squaresClassName - The class name of the squares.
 */
interface InteractiveGridPatternProps extends React.HTMLAttributes<HTMLDivElement> {
  width?: number;
  height?: number;
  squares?: [number, number]; // [horizontal, vertical]
  className?: string;
  squaresClassName?: string;
}

/**
 * HSL to RGB conversion utility
 */
const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
};

/**
 * Generate vivid rainbow hover color for each grid square with gradient and glow
 * Uses index as seed for consistent but random-looking colors
 */
const generateHoverColor = (index: number, isDark: boolean = false): {
  gradient: string;
  glow: string;
  rgb: [number, number, number]
} => {
  // Use index as seed for pseudo-random but consistent colors
  const seed = index * 2654435761; // Large prime number for better distribution
  const hue = (seed % 360); // Random hue from 0-360

  // Vivid colors with high saturation for both light and dark modes
  const saturation = 0.9; // Maximum vibrancy
  const lightness = isDark ? 0.6 : 0.5; // Balanced brightness for both modes

  // Convert HSL to RGB
  const [r, g, b] = hslToRgb(hue, saturation, lightness);

  // Create lighter and darker variants for gradient
  const lighterLightness = Math.min(lightness + 0.2, 0.9);
  const darkerLightness = Math.max(lightness - 0.2, 0.1);

  const [r1, g1, b1] = hslToRgb(hue, saturation, lighterLightness);
  const [r2, g2, b2] = hslToRgb(hue, saturation, darkerLightness);

  return {
    gradient: `linear-gradient(135deg, rgba(${r1}, ${g1}, ${b1}, 0.8) 0%, rgba(${r2}, ${g2}, ${b2}, 0.9) 100%)`,
    glow: `rgba(${r}, ${g}, ${b}, ${isDark ? 0.6 : 0.4})`,
    rgb: [r, g, b]
  };
};

/**
 * The InteractiveGridPattern component with dynamic color algorithm.
 *
 * @see InteractiveGridPatternProps for the props interface.
 * @returns A React component with enhanced visual effects.
 */
export function InteractiveGridPattern({
  width = 40,
  height = 40,
  squares = [24, 24],
  className,
  squaresClassName,
  ...props
}: InteractiveGridPatternProps) {
  const [horizontal, vertical] = squares;
  const [hoveredSquare, setHoveredSquare] = useState<number | null>(null);

  // Detect dark mode using CSS media query
  const [isDark, setIsDark] = useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const checkDarkMode = () => {
      // Check both system preference and document class
      const systemDark = mediaQuery.matches;
      const documentDark = document.documentElement.classList.contains('dark');
      setIsDark(systemDark || documentDark);
    };

    checkDarkMode();
    mediaQuery.addEventListener('change', checkDarkMode);

    // Also listen for class changes on document
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => {
      mediaQuery.removeEventListener('change', checkDarkMode);
      observer.disconnect();
    };
  }, []);

  // Memoize color calculations for performance
  const squareColors = useMemo(() => {
    return Array.from({ length: horizontal * vertical }).map((_, index) => {
      return generateHoverColor(index, isDark);
    });
  }, [horizontal, vertical, isDark]);

  return (
    <div
      {...props}
      className={cn(
        "absolute inset-0 h-full w-full",
        isDark ? "bg-black" : "bg-white",
        className,
      )}
      style={{ width: width * horizontal, height: height * vertical, ...props.style }}
    >
      <svg
        width={width * horizontal}
        height={height * vertical}
        className="absolute inset-0 h-full w-full"
      >
        {/* Enhanced filter for vivid glow effect */}
        <defs>
          {squareColors.map((color, index) => (
            <filter key={`glow-${index}`} id={`glow-${index}`} x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
              <feColorMatrix values={`1 0 0 0 ${color.rgb[0]/255} 0 1 0 0 ${color.rgb[1]/255} 0 0 1 0 ${color.rgb[2]/255} 0 0 0 1 0`}/>
            </filter>
          ))}
          {squareColors.map((color, index) => (
            <linearGradient key={`gradient-${index}`} id={`gradient-${index}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={`rgba(${color.rgb[0]}, ${color.rgb[1]}, ${color.rgb[2]}, 0.8)`} />
              <stop offset="100%" stopColor={`rgba(${Math.max(0, color.rgb[0] - 40)}, ${Math.max(0, color.rgb[1] - 40)}, ${Math.max(0, color.rgb[2] - 40)}, 0.9)`} />
            </linearGradient>
          ))}
        </defs>

        {/* Render grid squares with gradients and glow effects */}
        {Array.from({ length: horizontal * vertical }).map((_, index) => {
          const x = (index % horizontal) * width;
          const y = Math.floor(index / horizontal) * height;
          const isHovered = hoveredSquare === index;

          return (
            <g key={index}>
              {/* Glow effect layer (only visible on hover) */}
              {isHovered && (
                <rect
                  x={x}
                  y={y}
                  width={width}
                  height={height}
                  fill={`url(#gradient-${index})`}
                  filter={`url(#glow-${index})`}
                  className="opacity-60"
                />
              )}

              {/* Main square */}
              <rect
                x={x}
                y={y}
                width={width}
                height={height}
                fill={isHovered ? `url(#gradient-${index})` : "transparent"}
                stroke={isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}
                strokeWidth="0.5"
                className={cn(
                  "transition-all duration-300 ease-out cursor-pointer",
                  squaresClassName,
                )}
                style={{
                  filter: isHovered ? `drop-shadow(0 0 8px ${squareColors[index].glow})` : 'none'
                }}
                onMouseEnter={() => setHoveredSquare(index)}
                onMouseLeave={() => setHoveredSquare(null)}
              />
            </g>
          );
        })}
      </svg>
    </div>
  );
}
